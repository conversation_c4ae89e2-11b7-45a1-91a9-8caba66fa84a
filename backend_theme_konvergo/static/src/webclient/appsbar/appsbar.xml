<?xml version="1.0" encoding="UTF-8"?>


<templates xml:space="preserve">

	<t t-name="backend_theme_konvergo.AppsBar" owl="1">
		<div class="konvergo_apps_sidebar_panel">
			<div class="konvergo_apps_sidebar">
				<ul class="konvergo_apps_sidebar_menu">
					<t t-foreach="props.apps" t-as="app" t-key="app.id">
						<li t-attf-class="nav-item {{ app.active ? 'active' : '' }}">
							<a t-att-href="app.href" t-att-data-menu-id="app.id" t-att-data-menu-xmlid="app.xmlid" t-att-data-action-id="app.actionID" t-on-click.prevent="() => app.action()" class="nav-link" role="menuitem">
								<img t-if="app.webIconData" class="konvergo_apps_sidebar_icon" t-att-src="app.webIconData" />
								<img t-else="" class="konvergo_apps_sidebar_icon" src="/backend_theme_konvergo/static/img/default_icon.png" />
								<span class="konvergo_apps_sidebar_name">
									<t t-out="app.name"/>
								</span>
							</a>
						</li>
					</t>
				</ul>
			</div>
		</div>
	</t>

</templates>

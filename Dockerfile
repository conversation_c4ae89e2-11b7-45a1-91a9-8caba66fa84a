FROM quay.io/numigi/odoo-public:16.latest
LABEL maintainer="<EMAIL>"

USER root

ENV THIRD_PARTY_ADDONS /mnt/third-party-addons
RUN mkdir -p "${THIRD_PARTY_ADDONS}" && chown -R odoo "${THIRD_PARTY_ADDONS}"

USER odoo

COPY backend_theme_konvergo /mnt/extra-addons/backend_theme_konvergo
COPY ui_color_red /mnt/extra-addons/ui_color_red

COPY .docker_files/main /mnt/extra-addons/main
COPY .docker_files/odoo.conf /etc/odoo

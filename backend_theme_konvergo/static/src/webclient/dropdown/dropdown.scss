// DROP DOWN MENU

.dropdown-menu, .o-dropdown-menu {
    // fallback sans blur (pleine opacité)
    background: #fff;
  
    @supports ((-webkit-backdrop-filter: blur(30px)) or (backdrop-filter: blur(30px))) {
      background: rgba(#fff, 0.7);
      -webkit-backdrop-filter: blur(30px);
      backdrop-filter: blur(30px);
    }

    .dropdown-divider {
        border-top: 1px solid #b3b6b95c;
    }

    .dropdown-item, .o-autocomplete--dropdown-item, .dropdown-menu {
        margin: 5px;
        border-radius: $border-radius-sm;
        width: unset;

        &.focus, &.selected, &.selected:hover, &:hover {
            background: rgba(94, 103, 148, 0.12);
        }
    }
}
<?xml version="1.0" encoding="UTF-8" ?>

<template>

    <t t-name="backend_theme_konvergo.Pager" t-inherit="web.Pager" t-inherit-mode="extension" owl="1">
        <xpath expr="//span[hasclass('o_pager_counter')]" position="before">
            <button t-if="props.withAccessKey and !env.isSmall" type="button" tabindex="-1" aria-label="Refresh" data-hotkey="r" class="fa fa-refresh btn btn-secondary rounded me-1 konvergo_pager_refresh" t-att-class="this.autoLoadState.active ? 'konvergo_active_autoload' : ''" t-att-data-tooltip-info="this.getAutoloadTooltip()" t-att-data-tooltip-template="'backend_theme_konvergo.RefreshTooltip'" t-on-click.stop="() => this.navigate(0)" t-on-dblclick.stop="() => this.toggleAutoLoad()" />
        </xpath>
    </t>

    <t t-name="backend_theme_konvergo.RefreshTooltip" owl="1">
        <div>
            <p t-if="autoload" class="m-0">
                <t t-if="active">
		            Auto Refresh (<span t-out="interval"/>
 Seconds)
                </t>
                <t t-else="">
                    <p class="m-0">Single Click: Refresh View</li>
                    <p class="m-0">Double Click: Auto Refresh</li>
                </t>
            </p>
            <p t-else="" class="m-0">
            	Refresh View
            </p>
        </div>
    </t>

</template>


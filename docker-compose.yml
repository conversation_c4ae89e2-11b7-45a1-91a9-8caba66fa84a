services:
  odoo:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - odoo16-web-data:/var/lib/odoo
      - ./.log:/var/log/odoo
    ports:
      - "8069:8069"
      - "8071:8071"
    depends_on:
      - db
    command: odoo
    environment:
      - LOG_ODOO=/var/log/odoo
  db:
    image: postgres:16.0
    environment:
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_USER=odoo
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - odoo16-db-data:/var/lib/postgresql/data/pgdata
    expose:
      - 5432

volumes:
  odoo16-web-data:
  odoo16-db-data:

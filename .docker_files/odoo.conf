[options]
addons_path = /mnt/extra-addons,/mnt/third-party-addons
csv_internal_sep = ,
data_dir = /var/lib/odoo
db_host = db
db_maxconn = 64
db_name = dev
db_password = odoo
db_port = 5432
db_template = template1
db_user = odoo
dbfilter = dev
demo = {}
email_from = False
geoip_database = /usr/share/GeoIP/GeoLiteCity.dat
import_partial =
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 1500
limit_time_real = 1500
limit_time_real_cron = -1
list_db = True
log_db = False
log_db_level = warning
log_handler = :INFO
log_level = info
logfile = None
logrotate = False
longpolling_port = 8071
max_cron_threads = 1
osv_memory_age_limit = 1.0
osv_memory_count_limit = False
pg_path = None
pidfile = None
proxy_mode = False
reportgz = False
server_wide_modules = web
smtp_password = False
smtp_port = 25
smtp_server = localhost
smtp_ssl = False
smtp_user = False
syslog = False
test_commit = False
test_enable = False
test_file = False
test_report_directory = False
translate_modules = ['all']
unaccent = False
without_demo = False
workers = 0
xmlrpc = True
xmlrpc_interface =
xmlrpc_port = 8069
xmlrpcs = False

.konvergo_apps_search_container {
    margin: 4rem 2.5rem;
	width: 80%;
	.konvergo_apps_search_input {
		padding: 0.5rem 1.5rem;
        background-color: #FFF;
        border-radius: 100px;
		border: 1px solid hsla(0, 0%, 87%, 1);

		.konvergo_apps_search_icon {
			color: $konvergo-black-600; 
			margin-right: 1rem;
			font-size: 1.5rem;
		}
		input {
            display: block;
			height: 2.5rem;
            border: none;
            background: none;
			color: $konvergo-black-1000; 
            box-shadow: none;
            padding: 2px;
		}
		input::-webkit-search-cancel-button {
		  -webkit-appearance: none;
		}

		&:hover {
			box-shadow: 0 1px 6px hsla(0, 0%, 87%, 1);
			border-color: transparent;
		}
	}
}

.konvergo_apps_search_active {
	.konvergo_apps_search_input {
		box-shadow: 0 1px 6px hsla(0, 0%, 87%, 1);
		border-color: transparent;
	}
}

.konvergo_apps_search_menus {
    height: calc(100% - #{$o-navbar-height}); 
	margin-top: 1rem;
    overflow: auto;
    a {
		color: $konvergo-black-1000;
		margin: 0.5rem 0; 
    	display: block;
    	background-position: left;
	    background-repeat: no-repeat;
	    background-size: contain;
	    cursor: pointer;
	    line-height: 2.5rem;
	    padding-left: 3.5rem;
		border-radius: $border-radius-sm;

	    &:hover {
            background-color: rgba($black, 0.1);
        }
    }
}

.konvergo_apps_search_active ~ .o_app {
    display: none;
}

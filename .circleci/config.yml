version: 2.0

quay_io_login: &quay_io_login
  name: Login to Quay.io register
  command: docker login quay.io -u "${QUAY_USER}" -p "${QUAY_TOKEN}"

jobs:
  tests:
    machine: true
    steps:
       - checkout

       - run:
           <<: *quay_io_login

       - run:
          name: Build -- Init Database
          command: docker-compose run --rm odoo odoo --stop-after-init -i main

       - run:
          name: Setup Log Folder For Reports
          command: sudo mkdir -p .log && sudo chmod 777 .log

      #  - run:
      #     name: Run Test
      #     command: docker-compose run --rm odoo run_pytest.sh

       - store_test_results:
          path: .log
 
  # job that find the next tag for the current branch/repo and push the tag to github.
  # it will trigger the publish of a new docker image.
  auto-tag:
    machine: true
    steps:
      - checkout
      - run:
          <<: *quay_io_login
      - run:
          name: Get nws
          command: |
            curl -L $NWS_BIN_LOCATION > ./nws
            chmod +x ./nws
      - run:
          name: Set tag
          command: |
            ./nws circleci create-tag -t odoo-base

workflows:
  version: 2
  odoo:
    jobs:
      - tests:
          context: quay.io

      - auto-tag:
          context: nws
          requires:
            - tests
          filters:
            branches:
              only: /^1\d\.0/
